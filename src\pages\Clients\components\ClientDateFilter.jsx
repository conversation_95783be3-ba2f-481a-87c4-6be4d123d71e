import { DatePicker, Typography } from "antd";
import { setDateState } from "../../../store/actions/customers-action";
import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

export const DateFilters = () => {
  const { RangePicker } = DatePicker;

  // Função para desabilitar datas fora do período permitido
  const disabledDate = (current) => {
    if (!current) return false;

    const now = dayjs();
    const currentYear = now.year();
    const currentMonth = now.month(); // 0-11 (Janeiro = 0)

    const selectedYear = current.year();
    const selectedMonth = current.month();

    // Calcular diferença em meses
    const monthDiff = (currentYear - selectedYear) * 12 + (currentMonth - selectedMonth);

    // Desabilitar se for futuro (monthDiff < 0)
    if (monthDiff < 0) {
      console.log(`Bloqueando mês futuro: ${current.format('YYYY-MM')} (diff: ${monthDiff})`);
      return true;
    }

    if (monthDiff > 3) {
      console.log(`Bloqueando mês muito antigo: ${current.format('YYYY-MM')} (diff: ${monthDiff})`);
      return true;
    }

    return false;
  };

  return (
    <>
      <Typography.Text>Filtrar por período:</Typography.Text>
      <RangePicker
        picker="month"
        placeholder={["Inicio", "Fim"]}
        allowClear={true}
        locale={locale}
        style={{ borderRadius: "4px", height: "34px", width: "100%" }}
        disabledDate={disabledDate}
        onChange={(dates) => {
          if (dates && dates.length === 2) {
            setDateState({ field: "dtStart", value: dates[0] ? dates[0].format('YYYY-MM-DD') : null });
            setDateState({ field: "dtEnd", value: dates[1] ? dates[1].format('YYYY-MM-DD') : null });
          } else {
            setDateState({ field: "dtStart", value: null });
            setDateState({ field: "dtEnd", value: null });
          }
        }}
      />
    </>
  );
};
