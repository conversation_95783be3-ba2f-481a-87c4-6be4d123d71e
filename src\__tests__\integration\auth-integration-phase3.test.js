/**
 * Testes de Integração - Fase 3: Hooks e Contextos
 * Valida a integração dos hooks e contextos atualizados
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useAuthState } from '../../hooks/useAuthState';
import { AuthProvider, useAuthContext } from '../../contexts/AuthContext';
import { authService } from '../../services/authService';
import { httpOnlyAuthService } from '../../services/httpOnlyAuthService';

// Mock dos serviços
jest.mock('../../services/authService');
jest.mock('../../services/httpOnlyAuthService');
jest.mock('../../utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}));

// Componente de teste para useAuthState
const TestAuthStateComponent = () => {
  const {
    user,
    isLoading,
    isAuthenticated,
    authConfig,
    error,
    login,
    logout,
    checkAuth,
    getAuthInfo
  } = useAuthState();

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <div data-testid="authenticated">{isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="user">{user ? user.email : 'no-user'}</div>
      <div data-testid="error">{error || 'no-error'}</div>
      <div data-testid="auth-method">{getAuthInfo().method}</div>
      <button onClick={() => login('test-token', { email: '<EMAIL>' })}>Login</button>
      <button onClick={logout}>Logout</button>
      <button onClick={() => checkAuth(true)}>Check Auth</button>
    </div>
  );
};

// Componente de teste para AuthContext
const TestAuthContextComponent = () => {
  const auth = useAuthContext();

  return (
    <div>
      <div data-testid="context-authenticated">{auth.isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="context-user">{auth.user ? auth.user.email : 'no-user'}</div>
      <div data-testid="context-method">{auth.getAuthInfo ? auth.getAuthInfo().method : 'unknown'}</div>
    </div>
  );
};

describe('Integração de Hooks e Contextos - Fase 3', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Mock padrão dos serviços
    authService.initialize.mockResolvedValue(true);
    authService.getAuthConfig.mockReturnValue({ auth: { httpOnlySupported: false } });
    authService.migrateFromLocalStorage.mockResolvedValue(false);
    authService.isAuthenticated.mockResolvedValue(false);
    authService.getUserInfo.mockReturnValue({});
    authService.isUsingHttpOnlyCookies.mockReturnValue(false);
    authService.setToken.mockResolvedValue(true);
    authService.logout.mockResolvedValue();
    authService.setUserInfo.mockImplementation(() => {});
  });

  describe('useAuthState Hook', () => {
    test('deve inicializar corretamente', async () => {
      render(<TestAuthStateComponent />);

      // Verificar estado inicial
      expect(screen.getByTestId('loading')).toHaveTextContent('loading');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('no-user');

      // Aguardar inicialização
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      // Verificar se serviços foram chamados
      expect(authService.initialize).toHaveBeenCalled();
      expect(authService.migrateFromLocalStorage).toHaveBeenCalled();
      expect(authService.isAuthenticated).toHaveBeenCalled();
    });

    test('deve processar login corretamente', async () => {
      authService.setToken.mockResolvedValue(true);

      render(<TestAuthStateComponent />);

      // Aguardar inicialização
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      // Fazer login
      act(() => {
        screen.getByText('Login').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      });

      expect(authService.setToken).toHaveBeenCalledWith('test-token', { email: '<EMAIL>' });
    });

    test('deve processar logout corretamente', async () => {
      // Configurar usuário autenticado
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue({ email: '<EMAIL>' });

      render(<TestAuthStateComponent />);

      // Aguardar inicialização
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      });

      // Fazer logout
      act(() => {
        screen.getByText('Logout').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
        expect(screen.getByTestId('user')).toHaveTextContent('no-user');
      });

      expect(authService.logout).toHaveBeenCalled();
    });

    test('deve verificar autenticação com throttling', async () => {
      render(<TestAuthStateComponent />);

      // Aguardar inicialização
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      // Primeira verificação
      act(() => {
        screen.getByText('Check Auth').click();
      });

      // Segunda verificação imediata (deve ser throttled)
      act(() => {
        screen.getByText('Check Auth').click();
      });

      // authService.isAuthenticated deve ser chamado apenas uma vez adicional
      // (uma vez na inicialização + uma vez na verificação)
      await waitFor(() => {
        expect(authService.isAuthenticated).toHaveBeenCalledTimes(2);
      });
    });

    test('deve detectar método de autenticação', async () => {
      authService.isUsingHttpOnlyCookies.mockReturnValue(true);

      render(<TestAuthStateComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('auth-method')).toHaveTextContent('cookies');
      });

      authService.isUsingHttpOnlyCookies.mockReturnValue(false);

      // Re-render para atualizar
      render(<TestAuthStateComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('auth-method')).toHaveTextContent('localStorage');
      });
    });

    test('deve tratar erros na inicialização', async () => {
      authService.initialize.mockRejectedValue(new Error('Initialization failed'));

      render(<TestAuthStateComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Initialization failed');
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      });
    });
  });

  describe('AuthContext', () => {
    test('deve fornecer contexto de autenticação', async () => {
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue({ email: '<EMAIL>' });
      authService.isUsingHttpOnlyCookies.mockReturnValue(true);

      render(
        <AuthProvider>
          <TestAuthContextComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('context-authenticated')).toHaveTextContent('authenticated');
        expect(screen.getByTestId('context-user')).toHaveTextContent('<EMAIL>');
        expect(screen.getByTestId('context-method')).toHaveTextContent('cookies');
      });
    });

    test('deve lançar erro quando usado fora do provider', () => {
      // Suprimir console.error para este teste
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestAuthContextComponent />);
      }).toThrow('useAuthContext deve ser usado dentro de um AuthProvider');

      consoleSpy.mockRestore();
    });
  });

  describe('Integração com Serviços', () => {
    test('deve usar httpOnlyAuthService quando disponível', async () => {
      authService.isUsingHttpOnlyCookies.mockReturnValue(true);
      authService.getAuthConfig.mockReturnValue({ auth: { httpOnlySupported: true } });

      render(<TestAuthStateComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('auth-method')).toHaveTextContent('cookies');
      });

      expect(authService.initialize).toHaveBeenCalled();
    });

    test('deve fazer fallback para localStorage quando necessário', async () => {
      authService.isUsingHttpOnlyCookies.mockReturnValue(false);
      authService.getAuthConfig.mockReturnValue({ auth: { httpOnlySupported: false } });

      render(<TestAuthStateComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('auth-method')).toHaveTextContent('localStorage');
      });
    });
  });
});
