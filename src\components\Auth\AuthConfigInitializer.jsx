/**
 * Inicializador de Configuração de Autenticação
 * Detecta suporte a cookies HttpOnly e configura o sistema adequadamente
 */

import React, { useEffect, useState } from 'react';
import { authConfigManager } from '../../utils/authConfig';
import { logger } from '../../utils/logger';

const INIT_STATES = {
  INITIALIZING: 'initializing',
  SUCCESS: 'success',
  ERROR: 'error',
  FALLBACK: 'fallback'
};

export const AuthConfigInitializer = ({ children }) => {
  const [initState, setInitState] = useState(INIT_STATES.INITIALIZING);
  const [error, setError] = useState(null);

  useEffect(() => {
    const initializeAuthConfig = async () => {
      try {
        setInitState(INIT_STATES.INITIALIZING);
        setError(null);

        logger.info('Inicializando configuração de autenticação...');

        // Detectar suporte a cookies HttpOnly
        const httpOnlySupported = await authConfigManager.detectHttpOnlySupport();

        if (httpOnlySupported) {
          logger.info('✅ Cookies HttpOnly suportados - modo seguro ativado');
          setInitState(INIT_STATES.SUCCESS);
        } else {
          logger.warn('⚠️ Cookies HttpOnly não suportados - usando fallback para localStorage');
          setInitState(INIT_STATES.FALLBACK);
        }

      } catch (error) {
        logger.error('❌ Erro na inicialização da configuração de autenticação:', error);
        setError(error.message);
        setInitState(INIT_STATES.ERROR);
        
        // Forçar fallback em caso de erro
        authConfigManager.setHttpOnlySupport(false);
      }
    };

    initializeAuthConfig();
  }, []);

  if (initState === INIT_STATES.INITIALIZING) {
    return (
      <div className="auth-config-initializer">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Configurando sistema de autenticação...</p>
        </div>
      </div>
    );
  }

  if (initState === INIT_STATES.ERROR) {
    return (
      <div className="auth-config-initializer">
        <div className="error-container">
          <h3>Erro na Configuração</h3>
          <p>Falha ao configurar o sistema de autenticação:</p>
          <code>{error}</code>
          <button 
            onClick={() => window.location.reload()}
            className="retry-button"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {process.env.NODE_ENV === 'development' && (
        <div className="auth-config-debug">
          <small>
            Auth Mode: {initState === INIT_STATES.SUCCESS ? 'HttpOnly Cookies' : 'localStorage Fallback'}
          </small>
        </div>
      )}
      {children}
    </>
  );
};

export default AuthConfigInitializer;
