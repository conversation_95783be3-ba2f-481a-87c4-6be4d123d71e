import axios from "axios";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export const dsmApiProvider = (jwt = "") => {
  const authConfig = window.__AUTH_CONFIG__;

  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_PERMISSION || "",
    withCredentials: true, // ✅ HABILITADO para cookies HttpOnly
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
  });

  instance?.interceptors?.request.use((config) => {
    // Priorizar cookies HttpOnly, fallback para localStorage
    if (!authConfig?.httpOnlySupported) {
      // Fallback: usar localStorage + Authorization header
      const token = jwt ? jwt : localStorage.getItem("jwt");
      if (token && (!config.headers.Authorization || config.headers.Authorization.length < 15)) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    // Se httpOnlySupported=true, cookies são enviados automaticamente

    return config;
  });

  return instance;
};

export const dsmApiProviderDynamic = async (jwt = "") => {
  const apiUrl = await getApiUrl();

  if (jwt) {
    return axios.create({
      baseURL: apiUrl,
      headers: {
        Authorization: `Bearer ${jwt}`,
        'Content-Type': 'application/json'
      },
      withCredentials: true // ✅ Usar cookies HttpOnly quando disponível
    });
  }

  // Usar instância com suporte a cookies HttpOnly + fallback
  return axios.create({
    baseURL: apiUrl,
    headers: {
      'Content-Type': 'application/json'
    },
    withCredentials: true 
  });
};

export function getDsmHeader(tableName = "", jwt = "") {
  // Função legacy mantida para compatibilidade
  // Nota: Novos códigos devem usar cookies HttpOnly + withCredentials
  const token = jwt ? jwt : localStorage.getItem("jwt");

  let headers = {
    'Content-Type': 'application/json'
  };

  // Adicionar Authorization apenas se token existir (fallback)
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  if (tableName) {
    headers["dynamodb"] = tableName;
  }

  return headers;
}
