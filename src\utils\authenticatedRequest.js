/**
 * Utilitário para requisições autenticadas
 * Suporte a cookies HttpOnly com fallback para localStorage
 */

import axios from 'axios';
import { logger } from './logger';

/**
 * C<PERSON>r instância axios autenticada
 */
export const createAuthenticatedAxios = (baseURL = null) => {
  const instance = axios.create({
    baseURL: baseURL || process.env.REACT_APP_API_PERMISSION,
    withCredentials: true, 
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  });

  // Interceptor para logging
  instance.interceptors.request.use(
    (config) => {
      logger.debug('Authenticated Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL
      });
      return config;
    },
    (error) => {
      logger.error('Erro no request autenticado:', error);
      return Promise.reject(error);
    }
  );

  // Interceptor para tratamento de respostas
  instance.interceptors.response.use(
    (response) => {
      logger.debug('Authenticated Response:', {
        status: response.status,
        url: response.config?.url
      });
      return response;
    },
    (error) => {
      logger.error('Erro na response autenticada:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.message
      });
      return Promise.reject(error);
    }
  );

  return instance;
};

/**
 * Fazer requisição GET autenticada
 */
export const authenticatedGet = async (url, config = {}) => {
  const instance = createAuthenticatedAxios();
  return instance.get(url, config);
};

/**
 * Fazer requisição POST autenticada
 */
export const authenticatedPost = async (url, data = {}, config = {}) => {
  const instance = createAuthenticatedAxios();
  return instance.post(url, data, config);
};

/**
 * Fazer requisição PUT autenticada
 */
export const authenticatedPut = async (url, data = {}, config = {}) => {
  const instance = createAuthenticatedAxios();
  return instance.put(url, data, config);
};

/**
 * Fazer requisição DELETE autenticada
 */
export const authenticatedDelete = async (url, config = {}) => {
  const instance = createAuthenticatedAxios();
  return instance.delete(url, config);
};

/**
 * Fazer requisição PATCH autenticada
 */
export const authenticatedPatch = async (url, data = {}, config = {}) => {
  const instance = createAuthenticatedAxios();
  return instance.patch(url, data, config);
};

/**
 * Utilitário para requisições DynamoDB
 */
export const dynamoRequest = {
  /**
   * GET para DynamoDB
   */
  get: async (tableName, endpoint = 'read/all/0', config = {}) => {
    const instance = createAuthenticatedAxios();
    return instance.get(endpoint, {
      ...config,
      headers: {
        ...config.headers,
        dynamodb: tableName,
        'Content-Type': 'application/json'
      }
    });
  },

  /**
   * POST para DynamoDB
   */
  post: async (tableName, data, endpoint = 'create', config = {}) => {
    const instance = createAuthenticatedAxios();
    return instance.post(endpoint, data, {
      ...config,
      headers: {
        ...config.headers,
        dynamodb: tableName,
        'Content-Type': 'application/json'
      }
    });
  },

  /**
   * PUT para DynamoDB
   */
  put: async (tableName, data, endpoint = 'update', config = {}) => {
    const instance = createAuthenticatedAxios();
    return instance.put(endpoint, data, {
      ...config,
      headers: {
        ...config.headers,
        dynamodb: tableName,
        'Content-Type': 'application/json'
      }
    });
  },

  /**
   * DELETE para DynamoDB
   */
  delete: async (tableName, id, endpoint = 'delete', config = {}) => {
    const instance = createAuthenticatedAxios();
    return instance.delete(`${endpoint}/${id}`, {
      ...config,
      headers: {
        ...config.headers,
        dynamodb: tableName,
        'Content-Type': 'application/json'
      }
    });
  }
};

/**
 * Utilitário para requisições de upload
 */
export const uploadRequest = {
  /**
   * Upload de arquivo
   */
  uploadFile: async (file, endpoint = '/upload', config = {}) => {
    const formData = new FormData();
    formData.append('file', file);

    const instance = createAuthenticatedAxios();
    return instance.post(endpoint, formData, {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * Upload múltiplo
   */
  uploadMultiple: async (files, endpoint = '/upload/multiple', config = {}) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    const instance = createAuthenticatedAxios();
    return instance.post(endpoint, formData, {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

/**
 * Utilitário para requisições de contratos
 */
export const contractRequest = {
  /**
   * Listar contratos
   */
  list: async (config = {}) => {
    return dynamoRequest.get(`${process.env.REACT_APP_STAGE}-contracts`, 'read/all/0', config);
  },

  /**
   * Obter contrato por ID
   */
  getById: async (id, config = {}) => {
    return dynamoRequest.get(`${process.env.REACT_APP_STAGE}-contracts`, `read/id/${id}`, config);
  },

  /**
   * Criar contrato
   */
  create: async (contractData, config = {}) => {
    return dynamoRequest.post(`${process.env.REACT_APP_STAGE}-contracts`, contractData, 'create', config);
  },

  /**
   * Atualizar contrato
   */
  update: async (contractData, config = {}) => {
    return dynamoRequest.put(`${process.env.REACT_APP_STAGE}-contracts`, contractData, 'update', config);
  },

  /**
   * Deletar contrato
   */
  delete: async (id, config = {}) => {
    return dynamoRequest.delete(`${process.env.REACT_APP_STAGE}-contracts`, id, 'delete', config);
  }
};

export default {
  createAuthenticatedAxios,
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedDelete,
  authenticatedPatch,
  dynamoRequest,
  uploadRequest,
  contractRequest
};
