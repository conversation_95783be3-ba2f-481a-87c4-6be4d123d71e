import axios from "axios";
import { dsmProvider } from "../../provider/dsm-provider";
import { setUploadFilesState } from "../../store/actions/files-action";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { dynamoDelete } from "../../service/apiDsmDynamo";
import { authService } from "../../services/authService";

export async function getCustomers() {
  try {
    const provider = dsmProvider();
    const { data } = await provider.get("/customers/read/status?status=1", {
      headers: {
        dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
        'Content-Type': 'application/json'
      },
      withCredentials: true 
    });

    setUploadFilesState({ field: "customers", value: data.data.Items });
  } catch (error) {
    setUploadFilesState({ field: "customers", value: [] });
    console.log(error);
  }
}

export async function getFiles() {
  const files = await dynamoGet(`${process.env.REACT_APP_STAGE}-files-archive`);
  setUploadFilesState({ field: "files", value: files });
}

export async function getTags() {
  const tags = await dynamoGet(`${process.env.REACT_APP_STAGE}-files-tags`);
  setUploadFilesState({ field: "allTags", value: tags });
}

export async function dispatchEmail(files) {
  const filesMessage = [];
  const [firstFile] = files;

  for (const file of files) {
    const tags = getFileTags(file).join(", ");
    filesMessage.push(
      `
        <p>Nome do arquivo: ${file.name}</p>
        <p>Categoria: ${file.category}</p>
        <p>Tags: ${tags}</p>
      `
    );
  }

  const template = `
    <p>Olá, tudo bem?</p>
    ${
      filesMessage.length > 1
        ? "<p>Arquivos acabaram de ser compartilhados com você pela Darede lá no Portal do Cliente.<p/>"
        : "<p>Um arquivo acabou de ser compartilhado com você pela Darede lá no Portal do Cliente.<p/>"
    }
    <p>Você pode acessá-lo entrando no menu "Dashboard", no menu na parte esquerda do portal do cliente
    e selecionando a sua conta. Após concluir este passo, basta clicar no menu "Arquivos" e seu arquivo estará lá.</p>
    <p>Você pode acessar o portal do cliente em https://clientes.darede.com.br/ ou clicando aqui: ${
      process.env.REACT_APP_STAGE !== "prod"
        ? `<a href="${process.env.REACT_APP_STAGE}.clientes.darede.com.br">Portal do Cliente </a>`
        : `<a href="clientes.darede.com.br">Portal do Cliente</a>`
    }</p>
    
    <hr>
    ${filesMessage.join("<br /> <hr>")}
    `;

  const subject = `[DSM] - Compartilhamento de arquivo`;
  const encriptyBody = btoa(unescape(encodeURIComponent(template)));
  const encriptySubject = btoa(unescape(encodeURIComponent(subject)));
  let formData = new FormData();
  formData.append("htmlTemplate", encriptyBody);
  formData.append("emails", firstFile.emails);
  formData.append("subject", encriptySubject);

  const jwt = localStorage.getItem("jwt");

  const headers = { Authorization: jwt };

  axios
    .post(`${process.env.REACT_APP_API_PERMISSION}email/send`, formData, {
      headers,
    })
    .then(() => {
      return true;
    })
    .catch((e) => {
      console.log({ e });
      return false;
    });
}

export function getFileTags(file) {
  const tagNames = [];
  file?.tags?.forEach((t) => {
    tagNames.push(t);
  });
  return tagNames;
}

export const downloadFileS3 = async (file) => {
  const FileDownload = require("js-file-download");
  const backUrl = `${process.env.REACT_APP_API_NEW}s3/link`;
  const body = {
    bucket: `${process.env.REACT_APP_STAGE}-files-archive`,
    key: `${file.customerId}-${encodeURI(file.customerName)}/${file.id}.${
      file.extension
    }`,
    operation: "get",
  };
  const headers = {
    headers: {
      authorization: localStorage.getItem("jwt"),
    },
  };

  const {
    data: { data },
  } = await axios.post(backUrl, body, headers);

  await axios
    .get(data, {
      responseType: "blob",
    })
    .then((response) => {
      FileDownload(response.data, `${file.name}`);
      return true;
    })
    .catch((err) => {
      console.log("ERROR ON FILE DOWNLOAD", { err });
      return false;
    });
};

export const deleteFile = async (file) => {
  const backUrl = `${process.env.REACT_APP_API_NEW}delete/objects`;
  const body = {
    bucketName: `${process.env.REACT_APP_STAGE}-files-archive`,
    fileKeys: [`${file.customerId}-${encodeURI(file.customerName)}/${file.id}`],
  };
  const headers = {
    headers: {
      authorization: localStorage.getItem("jwt"),
    },
  };

  await axios.post(backUrl, body, headers);
  await dynamoDelete(`${process.env.REACT_APP_STAGE}-files-archive`, file.id);
};
