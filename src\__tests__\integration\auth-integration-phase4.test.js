/**
 * Testes de Integração - Fase 4: Endpoints Protegidos
 * Valida a atualização de endpoints para usar cookies HttpOnly
 */

import axios from 'axios';
import { 
  createAuthenticatedAxios,
  authenticatedGet,
  authenticatedPost,
  dynamoRequest,
  uploadRequest,
  contractRequest
} from '../../utils/authenticatedRequest';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  })),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn()
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}));

describe('Integração de Endpoints Protegidos - Fase 4', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createAuthenticatedAxios', () => {
    test('deve criar instância axios com withCredentials', () => {
      const mockInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const instance = createAuthenticatedAxios();

      expect(axios.create).toHaveBeenCalledWith({
        baseURL: process.env.REACT_APP_API_PERMISSION,
        withCredentials: true,
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      expect(mockInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockInstance.interceptors.response.use).toHaveBeenCalled();
    });

    test('deve aceitar baseURL customizada', () => {
      const customBaseURL = 'https://custom-api.com';
      const mockInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      createAuthenticatedAxios(customBaseURL);

      expect(axios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: customBaseURL,
          withCredentials: true
        })
      );
    });
  });

  describe('Métodos de requisição autenticada', () => {
    test('authenticatedGet deve fazer requisição GET', async () => {
      const mockInstance = {
        get: jest.fn().mockResolvedValue({ data: 'test' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const result = await authenticatedGet('/test-endpoint');

      expect(mockInstance.get).toHaveBeenCalledWith('/test-endpoint', {});
      expect(result).toEqual({ data: 'test' });
    });

    test('authenticatedPost deve fazer requisição POST', async () => {
      const mockInstance = {
        post: jest.fn().mockResolvedValue({ data: 'created' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const testData = { name: 'test' };
      const result = await authenticatedPost('/test-endpoint', testData);

      expect(mockInstance.post).toHaveBeenCalledWith('/test-endpoint', testData, {});
      expect(result).toEqual({ data: 'created' });
    });
  });

  describe('dynamoRequest', () => {
    test('get deve fazer requisição com header dynamodb', async () => {
      const mockInstance = {
        get: jest.fn().mockResolvedValue({ data: 'dynamo-data' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const tableName = 'test-table';
      const result = await dynamoRequest.get(tableName);

      expect(mockInstance.get).toHaveBeenCalledWith('read/all/0', {
        headers: {
          dynamodb: tableName,
          'Content-Type': 'application/json'
        }
      });
      expect(result).toEqual({ data: 'dynamo-data' });
    });

    test('post deve fazer requisição POST com header dynamodb', async () => {
      const mockInstance = {
        post: jest.fn().mockResolvedValue({ data: 'created' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const tableName = 'test-table';
      const testData = { id: 1, name: 'test' };
      const result = await dynamoRequest.post(tableName, testData);

      expect(mockInstance.post).toHaveBeenCalledWith('create', testData, {
        headers: {
          dynamodb: tableName,
          'Content-Type': 'application/json'
        }
      });
      expect(result).toEqual({ data: 'created' });
    });
  });

  describe('uploadRequest', () => {
    test('uploadFile deve fazer upload com FormData', async () => {
      const mockInstance = {
        post: jest.fn().mockResolvedValue({ data: 'uploaded' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      const result = await uploadRequest.uploadFile(mockFile);

      expect(mockInstance.post).toHaveBeenCalledWith(
        '/upload',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      expect(result).toEqual({ data: 'uploaded' });
    });

    test('uploadMultiple deve fazer upload de múltiplos arquivos', async () => {
      const mockInstance = {
        post: jest.fn().mockResolvedValue({ data: 'uploaded-multiple' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      const mockFiles = [
        new File(['test1'], 'test1.txt', { type: 'text/plain' }),
        new File(['test2'], 'test2.txt', { type: 'text/plain' })
      ];
      const result = await uploadRequest.uploadMultiple(mockFiles);

      expect(mockInstance.post).toHaveBeenCalledWith(
        '/upload/multiple',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      expect(result).toEqual({ data: 'uploaded-multiple' });
    });
  });

  describe('contractRequest', () => {
    test('list deve listar contratos', async () => {
      const mockInstance = {
        get: jest.fn().mockResolvedValue({ data: 'contracts-list' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      // Mock process.env
      const originalEnv = process.env.REACT_APP_STAGE;
      process.env.REACT_APP_STAGE = 'test';

      const result = await contractRequest.list();

      expect(mockInstance.get).toHaveBeenCalledWith('read/all/0', {
        headers: {
          dynamodb: 'test-contracts',
          'Content-Type': 'application/json'
        }
      });
      expect(result).toEqual({ data: 'contracts-list' });

      // Restore env
      process.env.REACT_APP_STAGE = originalEnv;
    });

    test('getById deve obter contrato por ID', async () => {
      const mockInstance = {
        get: jest.fn().mockResolvedValue({ data: 'contract-detail' }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      // Mock process.env
      const originalEnv = process.env.REACT_APP_STAGE;
      process.env.REACT_APP_STAGE = 'test';

      const contractId = '123';
      const result = await contractRequest.getById(contractId);

      expect(mockInstance.get).toHaveBeenCalledWith(`read/id/${contractId}`, {
        headers: {
          dynamodb: 'test-contracts',
          'Content-Type': 'application/json'
        }
      });
      expect(result).toEqual({ data: 'contract-detail' });

      // Restore env
      process.env.REACT_APP_STAGE = originalEnv;
    });
  });

  describe('Configuração withCredentials', () => {
    test('todas as instâncias devem ter withCredentials: true', () => {
      const mockInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      axios.create.mockReturnValue(mockInstance);

      // Testar diferentes métodos
      createAuthenticatedAxios();
      
      // Verificar se todas as chamadas axios.create incluem withCredentials: true
      const calls = axios.create.mock.calls;
      calls.forEach(call => {
        expect(call[0]).toHaveProperty('withCredentials', true);
      });
    });
  });
});
