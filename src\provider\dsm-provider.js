import axios from "axios";

export const dsmProvider = () => {
  const authConfig = window.__AUTH_CONFIG__;

  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_PERMISSION || "",
    withCredentials: true, 
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
  });

  instance.interceptors.request.use((config) => {
    if (!authConfig?.httpOnlySupported) {
      const jwt = localStorage.getItem("jwt");
      if (jwt) {
        config.headers.Authorization = jwt;
      }
    }
    // Se httpOnlySupported=true, cookies são enviados automaticamente
    return config;
  });

  return instance;
};
