/**
 * HttpOnly Authentication Service
 * Gerencia autenticação com cookies HttpOnly e fallback para localStorage
 */

import axios from 'axios';
import { logger } from '../utils/logger';
import { authConfigManager } from '../utils/authConfig';

class HttpOnlyAuthService {
  constructor() {
    this.apiBaseUrl = process.env.REACT_APP_API_PERMISSION;
    this.authConfig = null;
    this.isInitialized = false;
  }

  /**
   * Inicializar configuração de autenticação
   */
  async initializeAuth() {
    try {
      logger.debug('Inicializando HttpOnlyAuthService...');
      
      // Detectar suporte a cookies HttpOnly
      const response = await axios.get(`${this.apiBaseUrl}auth/config`, {
        withCredentials: true,
        timeout: 10000
      });

      this.authConfig = response.data;
      this.isInitialized = true;

      // Configurar axios globalmente baseado no suporte
      if (this.authConfig.auth?.httpOnlySupported) {
        axios.defaults.withCredentials = true;
        authConfigManager.setHttpOnlySupport(true);
        logger.info('✅ Cookies HttpOnly suportados - modo seguro ativado');
      } else {
        authConfigManager.setHttpOnlySupport(false);
        logger.warn('⚠️ Cookies HttpOnly não suportados - usando fallback');
      }

      return this.authConfig;
    } catch (error) {
      logger.error('Erro ao inicializar autenticação:', error);
      
      // Fallback para localStorage
      this.authConfig = { auth: { httpOnlySupported: false } };
      this.isInitialized = true;
      authConfigManager.setHttpOnlySupport(false);
      
      throw error;
    }
  }

  /**
   * Definir token de autenticação
   */
  async setAuthToken(token, userInfo) {
    try {
      if (!this.isInitialized) {
        await this.initializeAuth();
      }

      if (this.authConfig?.auth?.httpOnlySupported) {
        // Usar cookies HttpOnly
        logger.debug('Definindo token via cookies HttpOnly');
        
        const response = await axios.post(`${this.apiBaseUrl}auth/set-token`, {
          token,
          userInfo
        }, {
          withCredentials: true
        });

        if (response.status === 200) {
          // Armazenar apenas dados não sensíveis no localStorage
          this.setUserInfo(userInfo);
          logger.info('Token definido com sucesso via cookies HttpOnly');
          return true;
        }
        
        throw new Error('Falha ao definir token no servidor');
      } else {
        // Fallback para localStorage
        logger.debug('Fallback: definindo token no localStorage');
        localStorage.setItem('jwt', token);
        this.setUserInfo(userInfo);
        return true;
      }
    } catch (error) {
      logger.error('Erro ao definir token:', error);
      
      // Fallback para localStorage em caso de erro
      logger.warn('Usando fallback para localStorage devido ao erro');
      localStorage.setItem('jwt', token);
      this.setUserInfo(userInfo);
      return true;
    }
  }

  /**
   * Verificar se usuário está autenticado
   */
  async isAuthenticated() {
    try {
      if (!this.isInitialized) {
        await this.initializeAuth();
      }

      if (this.authConfig?.auth?.httpOnlySupported) {
        // Verificar via endpoint (cookies enviados automaticamente)
        logger.debug('Verificando autenticação via cookies HttpOnly');
        
        const response = await axios.get(`${this.apiBaseUrl}auth/verify`, {
          withCredentials: true,
          timeout: 5000
        });
        
        return response.status === 200;
      } else {
        // Verificar localStorage
        logger.debug('Fallback: verificando autenticação via localStorage');
        const token = localStorage.getItem('jwt');
        if (!token) return false;

        // Verificar expiração do token
        const { verifyExpTime } = await import('../service/verifyExpTime');
        const isExpired = await verifyExpTime(token);
        return !isExpired;
      }
    } catch (error) {
      logger.warn('Erro na verificação de autenticação:', error);
      return false;
    }
  }

  /**
   * Fazer logout
   */
  async logout() {
    try {
      if (this.authConfig?.auth?.httpOnlySupported) {
        // Logout via endpoint (limpa cookies no servidor)
        logger.debug('Fazendo logout via cookies HttpOnly');
        
        await axios.post(`${this.apiBaseUrl}auth/logout`, {}, {
          withCredentials: true
        });
        
        logger.info('Logout realizado com sucesso via cookies HttpOnly');
      }
    } catch (error) {
      logger.error('Erro no logout via cookies:', error);
    } finally {
      // Limpar localStorage sempre (dados não sensíveis)
      this.clearUserInfo();
      localStorage.removeItem('jwt'); // Limpar fallback também
      logger.debug('Dados locais limpos');
    }
  }

  /**
   * Armazenar informações do usuário (dados não sensíveis)
   */
  setUserInfo(userInfo) {
    const { name, email, permission, username } = userInfo;
    
    localStorage.setItem('@dsm/name', name || '');
    localStorage.setItem('@dsm/mail', email || '');
    localStorage.setItem('@dsm/permission', permission || '');
    localStorage.setItem('@dsm/username', username || '');
    localStorage.setItem('@dsm/time', new Date().toISOString());
    
    logger.debug('Informações do usuário armazenadas localmente');
  }

  /**
   * Recuperar informações do usuário
   */
  getUserInfo() {
    return {
      name: localStorage.getItem('@dsm/name'),
      email: localStorage.getItem('@dsm/mail'),
      permission: localStorage.getItem('@dsm/permission'),
      username: localStorage.getItem('@dsm/username'),
      loginTime: localStorage.getItem('@dsm/time'),
    };
  }

  /**
   * Limpar informações do usuário
   */
  clearUserInfo() {
    localStorage.removeItem('@dsm/name');
    localStorage.removeItem('@dsm/mail');
    localStorage.removeItem('@dsm/permission');
    localStorage.removeItem('@dsm/username');
    localStorage.removeItem('@dsm/time');
  }

  /**
   * Obter configuração atual
   */
  getAuthConfig() {
    return this.authConfig;
  }

  /**
   * Verificar se está usando cookies HttpOnly
   */
  isUsingHttpOnlyCookies() {
    return this.authConfig?.auth?.httpOnlySupported || false;
  }

  /**
   * Aguardar inicialização
   */
  async waitForInitialization(timeout = 10000) {
    const startTime = Date.now();
    
    while (!this.isInitialized && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (!this.isInitialized) {
      throw new Error('Timeout na inicialização do HttpOnlyAuthService');
    }
    
    return this.isInitialized;
  }
}

// Singleton instance
export const httpOnlyAuthService = new HttpOnlyAuthService();
export default httpOnlyAuthService;
