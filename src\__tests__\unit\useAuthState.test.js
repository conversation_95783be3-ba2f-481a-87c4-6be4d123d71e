/**
 * Testes Unitários - useAuthState Hook
 * Valida a funcionalidade do hook de estado de autenticação
 */

import { renderHook, act } from '@testing-library/react';
import { useAuthState } from '../../hooks/useAuthState';

// Mock dos serviços
jest.mock('../../services/authService', () => ({
  authService: {
    initialize: jest.fn().mockResolvedValue(true),
    getAuthConfig: jest.fn().mockReturnValue({ auth: { httpOnlySupported: false } }),
    migrateFromLocalStorage: jest.fn().mockResolvedValue(false),
    isAuthenticated: jest.fn().mockResolvedValue(false),
    getUserInfo: jest.fn().mockReturnValue({}),
    isUsingHttpOnlyCookies: jest.fn().mockReturnValue(false),
    setToken: jest.fn().mockResolvedValue(true),
    logout: jest.fn().mockResolvedValue(),
    setUserInfo: jest.fn()
  }
}));

jest.mock('../../services/httpOnlyAuthService', () => ({
  httpOnlyAuthService: {
    initializeAuth: jest.fn().mockResolvedValue({ auth: { httpOnlySupported: false } }),
    isUsingHttpOnlyCookies: jest.fn().mockReturnValue(false)
  }
}));

jest.mock('../../utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}));

describe('useAuthState Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  test('deve inicializar com estado padrão', async () => {
    const { result } = renderHook(() => useAuthState());

    // Estado inicial
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.user).toBe(null);
    expect(result.current.error).toBe(null);

    // Aguardar inicialização
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    expect(result.current.isLoading).toBe(false);
  });

  test('deve ter métodos de autenticação disponíveis', () => {
    const { result } = renderHook(() => useAuthState());

    expect(typeof result.current.login).toBe('function');
    expect(typeof result.current.logout).toBe('function');
    expect(typeof result.current.checkAuth).toBe('function');
    expect(typeof result.current.updateUser).toBe('function');
    expect(typeof result.current.hasPermission).toBe('function');
    expect(typeof result.current.getAuthInfo).toBe('function');
  });

  test('deve ter métodos de conveniência', () => {
    const { result } = renderHook(() => useAuthState());

    expect(typeof result.current.isUsingHttpOnlyCookies).toBe('function');
    expect(typeof result.current.getAuthConfig).toBe('function');
    expect(typeof result.current.isInitialized).toBe('boolean');
  });

  test('deve retornar informações de autenticação', () => {
    const { result } = renderHook(() => useAuthState());

    const authInfo = result.current.getAuthInfo();
    
    expect(authInfo).toHaveProperty('isUsingHttpOnlyCookies');
    expect(authInfo).toHaveProperty('authConfig');
    expect(authInfo).toHaveProperty('method');
    expect(authInfo.method).toBe('localStorage'); // Mock padrão
  });

  test('deve verificar permissões corretamente', () => {
    const { result } = renderHook(() => useAuthState());

    // Sem usuário
    expect(result.current.hasPermission('admin')).toBe(false);

    // Simular usuário com permissão
    act(() => {
      result.current.updateUser({ permission: 'admin' });
    });

    expect(result.current.hasPermission('admin')).toBe(true);
    expect(result.current.hasPermission('user')).toBe(true); // Admin tem todas as permissões
  });
});
