import axios from 'axios';
import { verifyExpTime } from '../service/verifyExpTime';
import { httpOnlyAuthService } from './httpOnlyAuthService';
import { logger } from '../utils/logger';

/**
 * Authentication Service - Sistema híbrido com cookies HttpOnly + localStorage fallback
 */
class AuthService {
  constructor() {
    this.apiBaseUrl = process.env.REACT_APP_API_PERMISSION || '';
    this.httpOnlyService = httpOnlyAuthService;
  }

  /**
   * Armazenar token JWT - prioriza cookies HttpOnly, fallback para localStorage
   */
  async setToken(token, userInfo = null) {
    try {
      // Tentar usar httpOnlyAuthService primeiro
      if (userInfo) {
        const success = await this.httpOnlyService.setAuthToken(token, userInfo);
        if (success) {
          logger.debug('Token definido via httpOnlyAuthService');
          return true;
        }
      }
    } catch (error) {
      logger.warn('Fallback para localStorage devido ao erro:', error);
    }

    localStorage.setItem('jwt', token);
    if (userInfo) {
      this.setUserInfo(userInfo);
    }
    logger.debug('Token definido via localStorage (fallback)');
    return true;
  }

  /**
   * Método legacy para compatibilidade
   */
  setTokenLegacy(token) {
    localStorage.setItem('jwt', token);
  }

  /**
   * Recuperar token JWT do localStorage
   */
  getToken() {
    return localStorage.getItem('jwt');
  }

  /**
   * Verificar se usuário está autenticado - prioriza cookies HttpOnly
   */
  async isAuthenticated() {
    try {
      // Tentar verificação via httpOnlyAuthService primeiro
      const authenticated = await this.httpOnlyService.isAuthenticated();
      logger.debug('Verificação de autenticação via httpOnlyAuthService:', authenticated);
      return authenticated;
    } catch (error) {
      logger.warn('Fallback para localStorage devido ao erro:', error);
    }

    const token = this.getToken();
    if (!token) return false;

    try {
      const isExpired = await verifyExpTime(token);
      const authenticated = !isExpired;
      logger.debug('Verificação de autenticação via localStorage (fallback):', authenticated);
      return authenticated;
    } catch (error) {
      logger.error('Erro ao verificar token:', error);
      return false;
    }
  }

  /**
   * Armazenar informações do usuário no localStorage (sistema original)
   */
  setUserInfo(userInfo) {
    const { name, email, permission, username } = userInfo;

    localStorage.setItem('@dsm/name', name || '');
    localStorage.setItem('@dsm/mail', email || '');
    localStorage.setItem('@dsm/permission', permission || '');
    localStorage.setItem('@dsm/username', username || '');
    localStorage.setItem('@dsm/time', new Date().toISOString());
  }

  /**
   * Recuperar informações do usuário do localStorage
   */
  getUserInfo() {
    return {
      name: localStorage.getItem('@dsm/name'),
      email: localStorage.getItem('@dsm/mail'),
      permission: localStorage.getItem('@dsm/permission'),
      username: localStorage.getItem('@dsm/username'),
      loginTime: localStorage.getItem('@dsm/time'),
    };
  }

  /**
   * Alias para getUserInfo (compatibilidade)
   */
  getUserData() {
    return this.getUserInfo();
  }

  /**
   * Fazer logout - limpa cookies HttpOnly e localStorage
   */
  async logout() {
    try {
      // Tentar logout via httpOnlyAuthService primeiro
      await this.httpOnlyService.logout();
      logger.debug('Logout realizado via httpOnlyAuthService');
    } catch (error) {
      logger.warn('Erro no logout httpOnly, limpando localStorage:', error);
    }

    // Limpar localStorage sempre (garantia)
    localStorage.removeItem('jwt');
    localStorage.removeItem('@dsm/name');
    localStorage.removeItem('@dsm/mail');
    localStorage.removeItem('@dsm/permission');
    localStorage.removeItem('@dsm/username');
    localStorage.removeItem('@dsm/time');
  }

  /**
   * Criar instância do axios com token de autenticação
   */
  createSimpleAxios(baseURL) {
    const token = this.getToken();

    return axios.create({
      baseURL: baseURL || this.apiBaseUrl,
      headers: token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      } : {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Criar instância do axios com autenticação (compatibilidade)
   */
  createAuthenticatedAxios(baseURL, options = {}) {
    const token = this.getToken();

    return axios.create({
      baseURL: baseURL || this.apiBaseUrl,
      headers: token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      } : {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Criar instância da API (compatibilidade)
   */
  createApiInstance(baseURL) {
    return this.createSimpleAxios(baseURL);
  }

  /**
   * Obter token do cookie (compatibilidade - retorna do localStorage)
   */
  getTokenFromCookie() {
    return this.getToken();
  }

  /**
   * Obter headers de autenticação (método principal)
   */
  getAuthHeaders() {
    const token = this.getToken();
    return token ? {
      'Authorization': token, // Formato original sem Bearer prefix ****
    } : {};
  }

  /**
   * Obter headers de autenticação legacy (compatibilidade)
   */
  getLegacyAuthHeaders() {
    return this.getAuthHeaders();
  }

  /**
   * Definir token de autenticação (método principal para httpOnly)
   */
  async setAuthToken(token, userInfo) {
    return await this.setToken(token, userInfo);
  }

  /**
   * Migrar dados do localStorage (se necessário)
   */
  async migrateFromLocalStorage() {
    try {
      const token = localStorage.getItem('jwt');
      const userInfo = this.getUserInfo();

      if (token && userInfo.name) {
        logger.debug('Migrando dados do localStorage para httpOnly');
        await this.setAuthToken(token, userInfo);
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Erro na migração do localStorage:', error);
      return false;
    }
  }

  /**
   * Verificar se está usando cookies HttpOnly
   */
  isUsingHttpOnlyCookies() {
    return this.httpOnlyService.isUsingHttpOnlyCookies();
  }

  /**
   * Obter configuração de autenticação
   */
  getAuthConfig() {
    return this.httpOnlyService.getAuthConfig();
  }

  /**
   * Inicializar serviço de autenticação
   */
  async initialize() {
    try {
      await this.httpOnlyService.initializeAuth();
      logger.debug('AuthService inicializado com sucesso');
      return true;
    } catch (error) {
      logger.warn('Erro na inicialização do AuthService:', error);
      return false;
    }
  }
}

export const authService = new AuthService();
export default authService;
