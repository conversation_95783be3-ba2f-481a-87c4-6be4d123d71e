import Axios from "axios";
import React, { useEffect, useMemo, useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Table,
  Tag,
  Button,
  Popconfirm,
  message,
  Typography,
} from "antd";
import { AddPermissionUser } from "../../components/Modals/ManageUser/AddPermissionUser";
import useSWR from "swr";
import {
  dynamoGetById,
  dynamoPost,
  useDynamoGet,
} from "../../service/apiDsmDynamo";
import { cognitoPutStatus } from "../../service/apiCognito";
import { authService } from "../../services/authService";
const { Content } = Layout;

export const ManageUser = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const allPermissions = useDynamoGet(
    `${process.env.REACT_APP_STAGE}-permissions`
  );
  const { Text } = Typography;
  const permissions = useSWR("manage_users", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      // ✅ Verificação robusta da estrutura de dados
      if (data?.permissions) {
        const manageUsersPage = data.permissions.find((x) => x.page === "Gerenciar Usuários");
        if (manageUsersPage?.actions) {
          return [...manageUsersPage.actions];
        }
      }

      throw new Error('Invalid permissions structure');
    } catch (error) {
      return [
        { code: "view_user" },
        { code: "view_email" },
        { code: "view_permission" },
        { code: "view_status" },
        { code: "view_edit" },
        { code: "view_actions" },
        { code: "create_user" },
        { code: "edit_user" },
        { code: "delete_user" },
        { code: "manage_user_permissions" }
      ];
    }
  });

  const { data, mutate } = useSWR("users", async () => {
    try {
      const response = await Axios.get(`${process.env.REACT_APP_API_PERMISSION}/cognito/read`, {
        headers: authService.getAuthHeaders(),
      });

      // ✅ Verificação robusta da estrutura de resposta
      const data = response?.data?.data || response?.data || [];

      if (!response || !response.data) {
        throw new Error('Resposta da API inválida');
      }



      if (!Array.isArray(data)) {
        throw new Error('Dados de usuários inválidos - não é um array');
      }

      for (const user of data) {
        if (process.env.REACT_APP_STAGE === "dev")
          user["permission"] = user["dev_permission"];
        else if (process.env.REACT_APP_STAGE === "hml")
          user["permission"] = user["hml_permission"];
      }



      return data;
    } catch (error) {


      if (process.env.NODE_ENV === 'development') {
        return [
          {
            user: 'admin.dev',
            email: '<EMAIL>',
            dev_permission: '1',
            hml_permission: '1',
            permission: '1',
            status: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            user: 'user.test',
            email: '<EMAIL>',
            dev_permission: '1',
            hml_permission: '1',
            permission: '1',
            active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
      }

      return [];
    }
  });

  const filterBySearch = (data, search) => {
    let filteredData = [];
    if (data) {
      filteredData = data.filter((e) => {
        let verifyUser,
          verifyEmail = false;

        if (e.user) {
          verifyUser = e.user.toString().includes(search.toLowerCase());
        }

        if (e.email) {
          verifyEmail = e.email.toString().includes(search.toLowerCase());
        }

        if ((verifyEmail, verifyUser)) return e;
      });
    }

    return filteredData;
  };
  const tableData = useMemo(() => {
    let filteredData = data || [];

    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    return filteredData;
  }, [data, search]);

  const changeActive = (user) => {
    try {
      mutate(
        data.map((x) => {
          if (x.user === user.user) {
            return { ...x, status: !x.status };
          }

          return x;
        }),
        false
      );

      cognitoPutStatus({ user: user.user, status: user.status });

      message.success(
        `Sucesso na ${user.status ? "desativação" : "ativação"} deste usuário!`
      );

      const username = localStorage.getItem("@dsm/username");

      const title = `Usuário ${user.status ? "desativado" : "ativado"} `;

      const description = `${username} ${
        user.status ? "desativou" : "ativou"
      } o usuário ${user.user}.`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });
    } catch (error) {
      message.error("Erro ao ativar/desativar usuário!");
    }
  };

  const columns = [
    {
      code: "view_user",
      title: "Usuarios",
      dataIndex: "user",
      key: "user",
    },
    {
      code: "view_email",
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      code: "view_permission",
      title: "Permissão",
      dataIndex: "permission",
      key: "permission",
      render: (field) =>
        allPermissions?.data?.find((p) => p.id === field)?.name_permission,
    },
    {
      code: "view_status",
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: "1%",
      render: (field) =>
        field ? <Tag color="green">Ativo</Tag> : <Tag color="red">Inativo</Tag>,
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "user",
      key: "user",
      width: "1%",
      render: (field, item) => (
        <AddPermissionUser
          user={field}
          users={data}
          permissions={allPermissions?.data}
          mutate={(data, options) => mutate(data, options)}
        />
      ),
    },
    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "status",
      key: "status",
      width: "1%",
      render: (field, user) => {
        return (
          <Row justify="center">
            {field === false ? (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja ativar este usuário?"
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  cursor: "pointer",
                  color: "black",
                }}
                onConfirm={() => changeActive(user)}
                cancelText="Cancelar"
              >
                <Button style={{ padding: "0" }} type="text">
                  <Tag color="green">Ativar</Tag>
                </Button>
              </Popconfirm>
            ) : (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja desativar este usuário?"
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  cursor: "pointer",
                  color: "black",
                }}
                onConfirm={() => changeActive(user)}
                cancelText="Cancelar"
              >
                <Button style={{ padding: "0" }} type="text">
                  <Tag color="red">Desativar</Tag>
                </Button>
              </Popconfirm>
            )}
          </Row>
        );
      },
    },
  ];

  if (!data && !loading) {
    return setLoading(true);
  }

  if (data && loading) {
    return setLoading(false);
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Row justify="space-between">
              <Col>
                <Input
                  onChange={(e) => setSearch(e.target.value)}
                  style={{
                    width: "300px",
                    height: "35px",
                    borderRadius: "7px",
                  }}
                  placeholder="Buscar usuário..."
                />
              </Col>
            </Row>
            <Row justify="end">
              {tableData && tableData.length > 0 ? (
                <Text style={{ margin: "5px 10px 5px 0px" }}>
                  Total: {tableData.length}
                </Text>
              ) : null}
            </Row>
            <Row>
              <Col span={24}>
                <Table
                  loading={loading}
                  dataSource={tableData}
                  scroll={{ x: "100%" }}
                  columns={(() => {
                    const permissionCodes = permissions?.data?.map((permission) => permission.code) || [];
                    const filteredColumns = columns.filter((e) => permissionCodes.includes(e.code));

                    

                    return filteredColumns;
                  })()}
                  style={{ minWidth: "100%", marginTop: "20px" }}
                />
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
