import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { authService } from '../services/authService';
import { httpOnlyAuthService } from '../services/httpOnlyAuthService';
import { logger } from '../utils/logger';

const AuthContext = createContext(null);

/**
 * Auth Provider Component
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authConfig, setAuthConfig] = useState(null);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        logger.debug('Inicializando estado de autenticação...');

        await authService.initialize();

        // Obter configuração de autenticação
        const config = authService.getAuthConfig();
        setAuthConfig(config);

        await authService.migrateFromLocalStorage();

        const authenticated = await authService.isAuthenticated();

        if (authenticated) {
          const userInfo = authService.getUserInfo();
          setUser(userInfo);
          setIsAuthenticated(true);
          logger.info('Usuário autenticado encontrado:', { email: userInfo.email });
        } else {
          setUser(null);
          setIsAuthenticated(false);
          logger.debug('Nenhum usuário autenticado encontrado');
        }
      } catch (error) {
        logger.error('Erro na inicialização da autenticação:', error);
        setUser(null);
        setIsAuthenticated(false);
        setAuthConfig({ auth: { httpOnlySupported: false } }); // Fallback
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (token, userInfo) => {
    try {
      setIsLoading(true);
      logger.debug('Iniciando processo de login...');

      const success = await authService.setAuthToken(token, userInfo);

      if (success) {
        setUser(userInfo);
        setIsAuthenticated(true);
        logger.info('Login realizado com sucesso:', {
          email: userInfo.email,
          method: authService.isUsingHttpOnlyCookies() ? 'cookies' : 'localStorage'
        });
        return { success: true };
      } else {
        logger.error('Falha ao definir token de autenticação');
        return { success: false, error: 'Failed to set authentication' };
      }
    } catch (error) {
      logger.error('Erro no login:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      logger.debug('Iniciando processo de logout...');

      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);

      logger.info('Logout realizado com sucesso');
    } catch (error) {
      logger.error('Erro no logout:', error);
      // Mesmo com erro, limpar estado local
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user info
  const updateUser = useCallback((newUserInfo) => {
    setUser(prevUser => ({ ...prevUser, ...newUserInfo }));
    authService.setUserInfo({ ...user, ...newUserInfo });
  }, [user]);

  const hasPermission = useCallback((requiredPermission) => {
    if (!user || !user.permission) return false;
    
    // Adicionar logica de permissão aqui
    return user.permission === requiredPermission || user.permission === 'admin';
  }, [user]);

  const refreshAuth = useCallback(async () => {
    try {
      const authenticated = await authService.isAuthenticated();
      
      if (authenticated) {
        const userInfo = authService.getUserInfo();
        setUser(userInfo);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
      
      return authenticated;
    } catch (error) {
      console.error('Error refreshing auth:', error);
      return false;
    }
  }, []);

  const value = {
    user,
    isLoading,
    isAuthenticated,
    authConfig,
    login,
    logout,
    updateUser,
    hasPermission,
    refreshAuth,
    isUsingHttpOnlyCookies: () => authService.isUsingHttpOnlyCookies(),
    getAuthConfig: () => authService.getAuthConfig(),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use authentication
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

/**
 * HOC for components that require authentication
 */
export const withAuth = (WrappedComponent) => {
  return function AuthenticatedComponent(props) {
    const { isAuthenticated, isLoading } = useAuth();
    
    if (isLoading) {
      return <div>Loading...</div>; 
    }
    
    if (!isAuthenticated) {
      return <div>Unauthorized</div>; 
    }
    
    return <WrappedComponent {...props} />;
  };
};

/**
 * Hook for creating authenticated API calls
 */
export const useAuthenticatedApi = () => {
  const { logout } = useAuth();

  const createApiInstance = useCallback(() => {
    // Criar instância com suporte a cookies HttpOnly
    const instance = axios.create({
      baseURL: process.env.REACT_APP_API_PERMISSION,
      withCredentials: true, 
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Interceptor para tratamento de 401
    instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    return instance;
  }, [logout]);

  return { createApiInstance };
};

export default useAuth;
