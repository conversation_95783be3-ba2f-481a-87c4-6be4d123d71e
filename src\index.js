// IMPORTANTE: Importar supressões PRIMEIRO (antes do React)
import "./utils/reactStrictModeSuppress";
import "./utils/suppressFindDOMNode";
import "./utils/antdWarningSuppress";
import "./utils/suppressPerformanceViolations";
import "./utils/preventNetworkErrors";

// CONFIGURAÇÃO CRÍTICA DO DAYJS - DEVE VIR ANTES DO REACT
import dayjs from 'dayjs';
import 'dayjs/locale/pt-br';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// Configurar todos os plugins necessários
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(dayOfYear);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

dayjs.locale('pt-br');


import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./styles/theme.css";
import "./styles/antd-custom.css";
import "./styles/layout-fixes.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { store, persistor } from "./store/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { initializeStorageCleanup } from "./utils/clearCorruptedStorage";
import { initializePerformanceOptimizations } from "./utils/performanceOptimization";
import { initializeErrorSuppression } from "./utils/errorSuppression";

initializeErrorSuppression();

import axios from 'axios';
if (process.env.REACT_APP_API_PERMISSION) {
  axios.defaults.baseURL = process.env.REACT_APP_API_PERMISSION;

} else {
  console.error('❌ REACT_APP_API_PERMISSION não definida no .env');
}

// Interceptor global para debug de requisições (DESABILITADO para evitar loops)
if (process.env.NODE_ENV === 'development' && false) { 
  axios.interceptors.request.use(
    (config) => {
      const fullUrl = config.baseURL ? `${config.baseURL}${config.url}` : config.url;
      console.log(`🌐 REQUEST: ${config.method?.toUpperCase()} ${fullUrl}`);
      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );
}

window.addEventListener('error', (event) => {
  console.error('Global Error:', event.error);
  console.error('Message:', event.message);
  console.error('Filename:', event.filename);
  console.error('Line:', event.lineno);
  console.error('Column:', event.colno);
  console.error('Stack:', event.error?.stack);

  const errorLog = {
    timestamp: new Date().toISOString(),
    message: event.message,
    filename: event.filename,
    line: event.lineno,
    column: event.colno,
    stack: event.error?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10))); // Keep last 10 errors
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);
  console.error('Promise:', event.promise);
  if (event.reason?.stack) {
    console.error('Stack:', event.reason.stack);
  }

  const errorLog = {
    timestamp: new Date().toISOString(),
    type: 'unhandledrejection',
    reason: event.reason?.toString(),
    stack: event.reason?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10)));
});

initializeStorageCleanup();

initializePerformanceOptimizations();

if (process.env.NODE_ENV === 'development') {
  window.showErrors = () => {
    const errors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
    return errors;
  };

  window.clearErrors = () => {
    sessionStorage.removeItem('errorLog');
  };
}

const container = document.getElementById("root");
const root = createRoot(container);

// Componente condicional para StrictMode
const AppWrapper = ({ children }) => {
  // Desabilitar StrictMode temporariamente para evitar warnings do findDOMNode do Antd
  // TODO: Reabilitar quando Antd for totalmente compatível com React 18
  const useStrictMode = process.env.REACT_APP_ENABLE_STRICT_MODE === 'true';

  if (useStrictMode) {
    return <React.StrictMode>{children}</React.StrictMode>;
  }

  return children;
};

root.render(
  <AppWrapper>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </AppWrapper>
);

reportWebVitals();
