import { otrsPost } from "../../../../service/apiOtrs";
import { otrsWebServicePost } from "../../../../service/otrsWebService";

export const formatInvokeStateMachineBody = ({
  formData,
  data,
  permissionSetAttachedToUser,
}) => {
  console.log(permissionSetAttachedToUser);
  if (!formData) {
    return {
      stage: process.env.REACT_APP_STAGE,
      id: data.id,
      action: "deny",
      stage: process.env.REACT_APP_STAGE,
      denier: (() => {
        try {
          const { authService } = require('../../../../services/authService');
          const userInfo = authService.getUserInfo();
          return userInfo.username;
        } catch (error) {
          return localStorage.getItem("@dsm/username"); 
        }
      })(),
      active: "Negado",
    };
  } else {
    switch (formData.action) {
      case "allow":
        return {
          id: data.id,
          action: "allow",
          username: data.username,
          stage: process.env.REACT_APP_STAGE,
          account_id: data.account_id,
          approver: localStorage.getItem("@dsm/username"),
          time: formData.time !== undefined ? formData.time * 60 : data.time,
          arn:
            permissionSetAttachedToUser?.arn ||
            "arn:aws:sso:::permissionSet/ssoins-7223894ba04bbb95/ps-d040c797f194d137",
        };
      default:
        return {
          id: formData.id,
          action: "revoke",
          username: formData.username,
          stage: process.env.REACT_APP_STAGE,
          account_id: formData.account_id,
          revoker: localStorage.getItem("@dsm/username"),
          active: "Revogado",
          role: localStorage.getItem("@dsm/username").replace(".", "-"),
          arn:
            permissionSetAttachedToUser?.arn ||
            "arn:aws:sso:::permissionSet/ssoins-7223894ba04bbb95/ps-d040c797f194d137",
        };
    }
  }
};

export const getPermissionSetFromUser = (permissionSets, data) => {
  let permissionSetAttachedToUser = permissionSets.filter((ps) => {
    if (ps.users) {
      return ps.users.find((user) => user.email.includes(data.username));
    }
  });
  return permissionSetAttachedToUser.shift();
};

export const otrsPermissionNotification = async (clientTicket, description) => {
  try {
    let ticket = await otrsPost("read/tickets/number", {
      params: parseInt(clientTicket),
    });

    ticket = ticket.data[0];

    await otrsWebServicePost("tickets/close", {
      title: "Notificação de liberação do acesso",
      communicationType: "Email",
      sender: localStorage.getItem("@dsm/mail"),
      description: description,
      tktNumber: parseInt(clientTicket),
      contract: 0,
      tktId: ticket.ticket_id,
      closed: "pending auto close+",
    });
  } catch (error) {
    console.log(error);
  }
};
