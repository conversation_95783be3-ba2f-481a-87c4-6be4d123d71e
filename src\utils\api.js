import axios from 'axios'
import { authService } from '../services/authService'
import { config } from './config'
import { logger } from './logger'

export const URLS = {
    DSM: process.env.REACT_APP_API_PERMISSION,
    PROPOSALS: process.env.REACT_APP_API_PROPOSALS,
    OTRS: process.env.REACT_APP_API_OTRS_BASE_URL,
    REPORTS: process.env.REACT_APP_API_REPORTS_URL
}

// Instância para DSM API
export const apiDsm = axios.create({
    baseURL: URLS.DSM,
    withCredentials: true, // ✅ HABILITADO para cookies HttpOnly
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.request.use(
        (config) => {
            // Priorizar cookies HttpOnly, fallback para localStorage
            const authConfig = window.__AUTH_CONFIG__;

            if (!authConfig?.httpOnlySupported) {
                // Fallback: usar localStorage + Authorization header
                try {
                    const token = authService.getToken();
                    if (token) {
                        config.headers.Authorization = `Bearer ${token}`;
                        logger.debug('Usando token do localStorage para DSM API (fallback)');
                    }
                } catch (error) {
                    logger.warn('Erro ao configurar autenticação para DSM API:', error);
                }
            } else {
                logger.debug('Usando cookies HttpOnly para DSM API');
            }
            // Se httpOnlySupported=true, cookies são enviados automaticamente

            logger.debug('DSM API Request:', {
                method: config.method?.toUpperCase(),
                url: config.url,
                hasAuth: !!authConfig?.httpOnlySupported || !!authService.getToken(),
                authMethod: authConfig?.httpOnlySupported ? 'cookies' : 'localStorage'
            });

            return config;
        },
        (error) => {
            logger.error('Erro no interceptor de request da DSM API:', error);
            return Promise.reject(error);
        }
    );
}

// Interceptor de response para auto-refresh e logging
if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.response.use(
        (response) => {
            // logger.debug('DSM API Response:', {
            //     status: response.status,
            //     url: response.config?.url
            // });
            return response;
        },
        async (error) => {
            const originalRequest = error.config;

            // Auto-refresh em caso de 401 (token expirado)
            if (error.response?.status === 401 && !originalRequest._retry) {
                originalRequest._retry = true;

                try {
                    const authConfig = window.__AUTH_CONFIG__;

                    if (authConfig?.httpOnlySupported) {
                        logger.debug('Tentando refresh via cookies HttpOnly');
                        await apiDsm.post('auth/refresh');

                        return apiDsm(originalRequest);
                    } else {
                        logger.debug('Fallback: token expirado, redirecionando para login');
                        throw new Error('Token expirado');
                    }
                } catch (refreshError) {
                    logger.error('Erro no refresh de token:', refreshError);

                    authService.logout();
                    window.location.href = '/login';
                    return Promise.reject(refreshError);
                }
            }

            logger.error('DSM API Error:', {
                status: error.response?.status,
                url: error.config?.url,
                message: error.message
            });
            return Promise.reject(error);
        }
    );
}

export const apiProposals = axios.create({
    baseURL: URLS.PROPOSALS
})

export const apiOTRS = axios.create({
    baseURL: URLS.OTRS,
    headers: {
        Authorization: config.OTRS.AUTH
    }
})

export const apiReports = axios.create({
    baseURL: URLS.REPORTS
})

export const getHeader = () => {
    return authService.getAuthHeaders();
}