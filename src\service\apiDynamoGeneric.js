// Service created to support some page requests to 'dev-dsm-back-end'
// api that is in the DSM AWS account.

import Axios from 'axios'
import { authService } from '../services/authService'

export const dynamoGenericGetById = async (tableName, id) => {
  const response = await Axios.get(
    process.env.REACT_APP_API_DYNAMO + 'read/id/' + id,
    {
      headers: {
        dynamodb: tableName,
        'Content-Type': 'application/json'
      },
      withCredentials: true 
    }
  )

  return response.data?.data?.Item || response.data?.Item || response.data
}

export const dynamoGenericGetAll = async tableName => {
  const response = await Axios.get(
    process.env.REACT_APP_API_DYNAMO + 'read/all/0',
    {
      headers: {
        dynamodb: tableName,
        ...authService.getLegacyAuthHeaders()
      }
    }
  )

  return response.data?.data?.Items || response.data?.Items || response.data
}

export const dynamoGenericPost = async (tableName, body) => {
  const { data } = await Axios.post(
    process.env.REACT_APP_API_DYNAMO + 'create',
    { ...body },
    {
      headers: {
        dynamodb: tableName,
        ...authService.getLegacyAuthHeaders()
      }
    }
  )

  return data
}

export const dynamoGenericPut = async (tableName, id, body) => {
  let obj = {}

  Object.entries(body).forEach(o => {
    if (o[0] !== 'id') {
      obj[o[0]] = { Action: 'PUT', Value: o[1] }
    }
  })

  const { data } = await Axios.put(
    process.env.REACT_APP_API_DYNAMO + 'update/' + id,
    { ...obj },
    {
      headers: {
        dynamodb: tableName,
        ...authService.getLegacyAuthHeaders()
      },
    }
  )

  return data
}

export const dynamoGenericDelete = async (tableName, id) => {
  const { data } = await Axios.delete(
    process.env.REACT_APP_API_DYNAMO + 'delete/' + id,
    {
      headers: {
        dynamodb: tableName,
        ...authService.getLegacyAuthHeaders()
      },
    }
  )

  return data
}
