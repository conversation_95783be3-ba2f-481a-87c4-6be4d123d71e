import { <PERSON><PERSON>, <PERSON><PERSON>, Row, Col, Form, Input, message } from "antd";
import axios from "axios";
import React, { useState } from "react";

export const PipedriveIntegration = () => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = async (contract) => {
    message.info("Este processo pode demorar um pouco, por favor aguarde.");
    setLoading(true);

    try {
      const {
        data: {
          data: { Payload },
        },
      } = await axios.get(
        `${process.env.REACT_APP_PIPEDRIVE_API}deals/register/${contract.id}`,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          withCredentials: true 
        }
      );

      if (JSON.parse(Payload).statusCode !== 200) {
        setLoading(false);
        return message.error(
          "Erro ao tentar trazer contrato do Pipedrive. Verifique o ID do Contrato.",
          5
        );
      }

      if (JSON.parse(JSON.parse(Payload).body).status !== "success") {
        // setShowModal(false);
        // form.resetFields();
        setLoading(false);
        return message.info(JSON.parse(JSON.parse(Payload).body).data);
      }

      message.success(
        "Sucesso ao cadastrar as informações recebidas do contrato! Em alguns minutos as informações já estarão disponíveis no portal."
      );
      setLoading(false);
      setShowModal(false);
      form.resetFields();
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        style={{ width: "100%" }}
        type="primary"
        onClick={() => setShowModal(true)}
      >
        Não encontrou o contrato?
      </Button>
      <Modal
        title="Informe o ID do contrato no Pipedrive"
        visible={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button onClick={() => setShowModal(false)}>Fechar</Button>,
          <Button
            loading={loading}
            type="primary"
            onClick={() => form.submit()}
          >
            Enviar
          </Button>,
        ]}
      >
        <Row style={{ marginTop: "1em" }}>
          <Col span={24}>
            <Form
              requiredMark={false}
              layout="vertical"
              form={form}
              onFinish={handleSubmit}
            >
              <Form.Item
                name="id"
                label="ID do Contrato no Pipedrive"
                rules={[{ required: true, message: "Por favor, informe o ID" }]}
              >
                <Input placeholder="Insira aqui o ID do Contrato registrado no Pipedrive" />
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
