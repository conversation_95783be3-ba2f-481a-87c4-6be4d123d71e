import Axios from "axios";
import { dsm<PERSON>rovider } from "../provider/dsm-provider";
import { authService } from "../services/authService";

export const generateToken = async () => {
  try {
    const provider = dsmProvider();
    const authConfig = window.__AUTH_CONFIG__;

    if (!authConfig?.httpOnlySupported) {
      // Fallback: usar localStorage + Authorization header
      let jwt = localStorage.getItem("jwt");
      const { data } = await provider.get("/cognito/access-token", {
        headers: {
          Authorization: jwt,
        },
      });
      return data.data;
    } else {
      // Usar cookies HttpOnly (sem headers Authorization)
      const { data } = await provider.get("/cognito/access-token");
      return data.data;
    }
  } catch (error) {
    console.log(error);

    // Fallback: tenta com configuração manual
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const { data } = await Axios.get(
        `${process.env.REACT_APP_API_PERMISSION}/cognito/access-token`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      return data.data;
    } catch (fallbackError) {
      console.error('Erro no fallback de geração de token:', fallbackError);
      throw fallbackError;
    }
  }
};

export const otrsGet = async (route, params) => {
  const token = await generateToken();

  const { data } = await Axios.get(
    process.env.REACT_APP_API_OTRS_BASE_URL + route,
    {
      headers: { Authorization: token },
      params,
    }
  );

  return data;
};

export const otrsPost = async (route, body) => {
  const token = await generateToken();

  console.log('🎯 otrsPost Debug:', {
    route,
    baseUrl: process.env.REACT_APP_API_OTRS_BASE_URL,
    fullUrl: process.env.REACT_APP_API_OTRS_BASE_URL + route,
    hasToken: !!token
  });

  if (route.includes("create/ticket")) {
    const { data } = await Axios.post(
      process.env.REACT_APP_API_OTRS_BASE_URL + route,
      { ...body },
      {
        headers: {
          Authorization: token,
          auth: process.env.REACT_APP_OTRS_HOURS_AUTH,
        },
      }
    );

    return data;
  }

  const { data } = await Axios.post(
    process.env.REACT_APP_API_OTRS_BASE_URL + route,
    { ...body },
    {
      headers: {
        Authorization:
          route === "read/hours/contract/new/rule"
            ? process.env.REACT_APP_OTRS_HOURS_AUTH
            : token,
      },
    }
  );

  return data;
};

export const createTicket = async (body) => {
  const token = await generateToken();

  const { data } = await Axios.post(
    process.env.REACT_APP_API_OTRS_BASE_URL + "create/ticket",
    body,
    {
      headers: {
        Authorization: token,
        auth: process.env.REACT_APP_OTRS_HOURS_AUTH,
      },
    }
  );

  return data;
};

export const otrsPut = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.put(
    process.env.REACT_APP_API_OTRS_BASE_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const ticketPost = async (route, body) => {
  const token = await generateToken();

  if (route.includes("create/ticket")) {
    const { data } = await Axios.post(
      process.env.REACT_APP_TICKET_API + route,
      { ...body },
      {
        headers: {
          Authorization: token,
          auth: process.env.REACT_APP_OTRS_HOURS_AUTH,
        },
      }
    );

    return data;
  } else {
    const { data } = await Axios.post(
      process.env.REACT_APP_TICKET_API + route,
      { ...body },
      {
        headers: { Authorization: token },
      }
    );

    return data;
  }
};

export const ticketPut = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.put(
    process.env.REACT_APP_TICKET_API + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const otrsDelete = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.delete(
    process.env.REACT_APP_API_BASE_OTRS_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};
